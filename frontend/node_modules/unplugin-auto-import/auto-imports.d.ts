/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
export {}
declare global {
  const $: typeof import('vue/macros')['$']
  const $$: typeof import('vue/macros')['$$']
  const $computed: typeof import('vue/macros')['$computed']
  const $customRef: typeof import('vue/macros')['$customRef']
  const $ref: typeof import('vue/macros')['$ref']
  const $shallowRef: typeof import('vue/macros')['$shallowRef']
  const $toRef: typeof import('vue/macros')['$toRef']
  const EffectScope: typeof import('vue')['EffectScope']
  const THREE: typeof import('three.js')
  const acceptHMRUpdate: typeof import('pinia')['acceptHMRUpdate']
  const afterAll: typeof import('vitest')['afterAll']
  const afterEach: typeof import('vitest')['afterEach']
  const afterUpdate: typeof import('svelte')['afterUpdate']
  const assert: typeof import('vitest')['assert']
  const backIn: typeof import('svelte/easing')['backIn']
  const backInOut: typeof import('svelte/easing')['backInOut']
  const backOut: typeof import('svelte/easing')['backOut']
  const beforeAll: typeof import('vitest')['beforeAll']
  const beforeEach: typeof import('vitest')['beforeEach']
  const beforeUpdate: typeof import('svelte')['beforeUpdate']
  const blur: typeof import('svelte/transition')['blur']
  const bounceIn: typeof import('svelte/easing')['bounceIn']
  const bounceInOut: typeof import('svelte/easing')['bounceInOut']
  const bounceOut: typeof import('svelte/easing')['bounceOut']
  const chai: typeof import('vitest')['chai']
  const circIn: typeof import('svelte/easing')['circIn']
  const circInOut: typeof import('svelte/easing')['circInOut']
  const circOut: typeof import('svelte/easing')['circOut']
  const computed: typeof import('vue')['computed']
  const createApp: typeof import('vue')['createApp']
  const createEventDispatcher: typeof import('svelte')['createEventDispatcher']
  const createPinia: typeof import('pinia')['createPinia']
  const createRef: typeof import('react')['createRef']
  const crossfade: typeof import('svelte/transition')['crossfade']
  const cubicIn: typeof import('svelte/easing')['cubicIn']
  const cubicInOut: typeof import('svelte/easing')['cubicInOut']
  const cubicOut: typeof import('svelte/easing')['cubicOut']
  const customDefault: typeof import('custom')['default']
  const customDefaultAlias: typeof import('custom')['default']
  const customNamed: typeof import('custom')['customNamed']
  const customRef: typeof import('vue')['customRef']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineStore: typeof import('pinia')['defineStore']
  const derived: typeof import('svelte/store')['derived']
  const describe: typeof import('vitest')['describe']
  const draw: typeof import('svelte/transition')['draw']
  const effectScope: typeof import('vue')['effectScope']
  const elasticIn: typeof import('svelte/easing')['elasticIn']
  const elasticInOut: typeof import('svelte/easing')['elasticInOut']
  const elasticOut: typeof import('svelte/easing')['elasticOut']
  const expect: typeof import('vitest')['expect']
  const expoIn: typeof import('svelte/easing')['expoIn']
  const expoInOut: typeof import('svelte/easing')['expoInOut']
  const expoOut: typeof import('svelte/easing')['expoOut']
  const fade: typeof import('svelte/transition')['fade']
  const flip: typeof import('svelte/animate')['flip']
  const fly: typeof import('svelte/transition')['fly']
  const forwardRef: typeof import('react')['forwardRef']
  const get: typeof import('svelte/store')['get']
  const getActivePinia: typeof import('pinia')['getActivePinia']
  const getAllContexts: typeof import('svelte')['getAllContexts']
  const getContext: typeof import('svelte')['getContext']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const h: typeof import('vue')['h']
  const hasContext: typeof import('svelte')['hasContext']
  const inject: typeof import('vue')['inject']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const it: typeof import('vitest')['it']
  const lazy: typeof import('react')['lazy']
  const linear: typeof import('svelte/easing')['linear']
  const mapActions: typeof import('pinia')['mapActions']
  const mapGetters: typeof import('pinia')['mapGetters']
  const mapState: typeof import('pinia')['mapState']
  const mapStores: typeof import('pinia')['mapStores']
  const mapWritableState: typeof import('pinia')['mapWritableState']
  const markRaw: typeof import('vue')['markRaw']
  const memo: typeof import('react')['memo']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onDestroy: typeof import('svelte')['onDestroy']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMount: typeof import('svelte')['onMount']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const provide: typeof import('vue')['provide']
  const quadIn: typeof import('svelte/easing')['quadIn']
  const quadInOut: typeof import('svelte/easing')['quadInOut']
  const quadOut: typeof import('svelte/easing')['quadOut']
  const quartIn: typeof import('svelte/easing')['quartIn']
  const quartInOut: typeof import('svelte/easing')['quartInOut']
  const quartOut: typeof import('svelte/easing')['quartOut']
  const quintIn: typeof import('svelte/easing')['quintIn']
  const quintInOut: typeof import('svelte/easing')['quintInOut']
  const quintOut: typeof import('svelte/easing')['quintOut']
  const reactive: typeof import('vue')['reactive']
  const readable: typeof import('svelte/store')['readable']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const scale: typeof import('svelte/transition')['scale']
  const setActivePinia: typeof import('pinia')['setActivePinia']
  const setContext: typeof import('svelte')['setContext']
  const setMapStoreSuffix: typeof import('pinia')['setMapStoreSuffix']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const sineIn: typeof import('svelte/easing')['sineIn']
  const sineInOut: typeof import('svelte/easing')['sineInOut']
  const sineOut: typeof import('svelte/easing')['sineOut']
  const slide: typeof import('svelte/transition')['slide']
  const spring: typeof import('svelte/motion')['spring']
  const startTransition: typeof import('react')['startTransition']
  const storeToRefs: typeof import('pinia')['storeToRefs']
  const suite: typeof import('vitest')['suite']
  const test: typeof import('vitest')['test']
  const tick: typeof import('svelte')['tick']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const triggerRef: typeof import('vue')['triggerRef']
  const tweened: typeof import('svelte/motion')['tweened']
  const unref: typeof import('vue')['unref']
  const useAttrs: typeof import('vue')['useAttrs']
  const useCallback: typeof import('react')['useCallback']
  const useContext: typeof import('react')['useContext']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useDebugValue: typeof import('react')['useDebugValue']
  const useDeferredValue: typeof import('react')['useDeferredValue']
  const useDialogPluginComponent: typeof import('quasar')['useDialogPluginComponent']
  const useEffect: typeof import('react')['useEffect']
  const useFormChild: typeof import('quasar')['useFormChild']
  const useId: typeof import('react')['useId']
  const useImperativeHandle: typeof import('react')['useImperativeHandle']
  const useInsertionEffect: typeof import('react')['useInsertionEffect']
  const useLayoutEffect: typeof import('react')['useLayoutEffect']
  const useMemo: typeof import('react')['useMemo']
  const useMeta: typeof import('quasar')['useMeta']
  const useQuasar: typeof import('quasar')['useQuasar']
  const useReducer: typeof import('react')['useReducer']
  const useRef: typeof import('react')['useRef']
  const useSlots: typeof import('vue')['useSlots']
  const useState: typeof import('react')['useState']
  const useSyncExternalStore: typeof import('react')['useSyncExternalStore']
  const useTransition: typeof import('react')['useTransition']
  const vi: typeof import('vitest')['vi']
  const vitest: typeof import('vitest')['vitest']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
  const writable: typeof import('svelte/store')['writable']
}

var O,x;function j(){if(x)return O;x=1;const{hasOwnProperty:y}=Object.prototype,d=(e,t={})=>{typeof t=="string"&&(t={section:t}),t.align=t.align===!0,t.newline=t.newline===!0,t.sort=t.sort===!0,t.whitespace=t.whitespace===!0||t.align===!0,t.platform=t.platform||typeof process<"u"&&process.platform,t.bracketedArray=t.bracketedArray!==!1;const s=t.platform==="win32"?`\r
`:`
`,r=t.whitespace?" = ":"=",c=[],o=t.sort?Object.keys(e).sort():Object.keys(e);let g=0;t.align&&(g=h(o.filter(n=>e[n]===null||Array.isArray(e[n])||typeof e[n]!="object").map(n=>Array.isArray(e[n])?`${n}[]`:n).concat([""]).reduce((n,i)=>h(n).length>=h(i).length?n:i)).length);let l="";const m=t.bracketedArray?"[]":"";for(const n of o){const i=e[n];if(i&&Array.isArray(i))for(const f of i)l+=h(`${n}${m}`).padEnd(g," ")+r+h(f)+s;else i&&typeof i=="object"?c.push(n):l+=h(n).padEnd(g," ")+r+h(i)+s}t.section&&l.length&&(l="["+h(t.section)+"]"+(t.newline?s+s:s)+l);for(const n of c){const i=k(n,".").join("\\."),f=(t.section?t.section+".":"")+i,u=d(e[n],{...t,section:f});l.length&&u.length&&(l+=s),l+=u}return l};function k(e,t){var s=0,r=0,c=0,o=[];do if(c=e.indexOf(t,s),c!==-1){if(s=c+t.length,c>0&&e[c-1]==="\\")continue;o.push(e.slice(r,c)),r=c+t.length}while(c!==-1);return o.push(e.slice(r)),o}const w=(e,t={})=>{t.bracketedArray=t.bracketedArray!==!1;const s=Object.create(null);let r=s,c=null;const o=/^\[([^\]]*)\]\s*$|^([^=]+)(=(.*))?$/i,g=e.split(/[\r\n]+/g),l={};for(const n of g){if(!n||n.match(/^\s*[;#]/)||n.match(/^\s*$/))continue;const i=n.match(o);if(!i)continue;if(i[1]!==void 0){if(c=A(i[1]),c==="__proto__"){r=Object.create(null);continue}r=s[c]=s[c]||Object.create(null);continue}const f=A(i[2]);let u;t.bracketedArray?u=f.length>2&&f.slice(-2)==="[]":(l[f]=(l?.[f]||0)+1,u=l[f]>1);const a=u&&f.endsWith("[]")?f.slice(0,-2):f;if(a==="__proto__")continue;const p=i[3]?A(i[4]):!0,b=p==="true"||p==="false"||p==="null"?JSON.parse(p):p;u&&(y.call(r,a)?Array.isArray(r[a])||(r[a]=[r[a]]):r[a]=[]),Array.isArray(r[a])?r[a].push(b):r[a]=b}const m=[];for(const n of Object.keys(s)){if(!y.call(s,n)||typeof s[n]!="object"||Array.isArray(s[n]))continue;const i=k(n,".");r=s;const f=i.pop(),u=f.replace(/\\\./g,".");for(const a of i)a!=="__proto__"&&((!y.call(r,a)||typeof r[a]!="object")&&(r[a]=Object.create(null)),r=r[a]);r===s&&u===f||(r[u]=s[n],m.push(n))}for(const n of m)delete s[n];return s},_=e=>e.startsWith('"')&&e.endsWith('"')||e.startsWith("'")&&e.endsWith("'"),h=e=>typeof e!="string"||e.match(/[=\r\n]/)||e.match(/^\[/)||e.length>1&&_(e)||e!==e.trim()?JSON.stringify(e):e.split(";").join("\\;").split("#").join("\\#"),A=e=>{if(e=(e||"").trim(),_(e)){e.charAt(0)==="'"&&(e=e.slice(1,-1));try{e=JSON.parse(e)}catch{}}else{let t=!1,s="";for(let r=0,c=e.length;r<c;r++){const o=e.charAt(r);if(t)"\\;#".indexOf(o)!==-1?s+=o:s+="\\"+o,t=!1;else{if(";#".indexOf(o)!==-1)break;o==="\\"?t=!0:s+=o}}return t&&(s+="\\"),s.trim()}return e};return O={parse:w,decode:w,stringify:d,encode:d,safe:h,unsafe:A},O}var I=j();function S(y,d){return I.parse(y,d)}function $(y,d){return I.stringify(y,{whitespace:!0,...d})}export{S as parseINI,$ as stringifyINI};
